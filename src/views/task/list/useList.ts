import { ref, reactive } from 'vue'
import { getTaskPage, deleteTask, deleteTaskList, startTask } from '@/api/alg/task'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Task, QueryParams } from './types'

export function useList() {
  const loading = ref(true)
  const taskList = ref<Task[]>([])
  const total = ref(0)

  const queryParams = reactive<QueryParams>({
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    taskType: undefined,
    status: undefined,
    createTime: undefined
  })

  /** 查询列表 */
  const getList = async () => {
    loading.value = true
    try {
      const data = await getTaskPage(queryParams)
      taskList.value = data.list
      total.value = data.total
    } catch (error) {
      ElMessage.error('获取任务列表失败')
      console.error('获取任务列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 删除按钮操作 */
  const handleDelete = async (row: Task) => {
    try {
      await ElMessageBox.confirm(`确认删除任务"${row.name}"?`, '提示', {
        type: 'warning'
      })
      await deleteTask({ id: row.id })
      ElMessage.success('删除成功')
      await getList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
        console.error('删除任务失败:', error)
      }
    }
  }

  /** 执行任务操作 */
  const handleStart = async (row: Task) => {
    try {
      await ElMessageBox.confirm(`确认执行任务"${row.name}"?`, '提示', {
        type: 'warning'
      })
      await startTask({ id: row.id })
      ElMessage.success('任务执行成功')
      await getList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('任务执行失败')
        console.error('任务执行失败:', error)
      }
    }
  }

  /** 批量删除按钮操作 */
  const handleDeleteMultiple = async (ids: number[]) => {
    try {
      await ElMessageBox.confirm(`确认删除选中的 ${ids.length} 个任务?`, '提示', {
        type: 'warning'
      })
      await deleteTaskList({ ids: ids.join(',') })
      ElMessage.success('删除成功')
      await getList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
        console.error('批量删除任务失败:', error)
      }
    }
  }

  return {
    loading,
    taskList,
    total,
    queryParams,
    getList,
    handleQuery,
    handleDelete,
    handleDeleteMultiple,
    handleStart
  }
}
