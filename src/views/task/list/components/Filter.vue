<template>
  <el-form :model="localParams" ref="queryFormRef" :inline="true">
    <el-form-item label="名称" prop="name">
      <el-input
        v-model="localParams.name"
        placeholder="请输入任务或数据集名称"
        clearable
        class="!w-240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="数据集名称" prop="datasetName">
      <el-input
        v-model="localParams.datasetName"
        placeholder="请输入数据集名称"
        clearable
        class="!w-240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="数据类型" prop="taskType">
      <el-select
        v-model="localParams.taskType"
        placeholder="全部"
        clearable
        class="!w-240px"
      >
        <el-option
          v-for="(label, value) in TASK_TYPE_LABELS"
          :key="value"
          :label="label"
          :value="Number(value)"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="状态" prop="status">
      <el-select v-model="localParams.status" placeholder="全部" clearable class="!w-240px">
        <el-option
          v-for="(label, value) in TASK_STATUS_LABELS"
          :key="value"
          :label="label"
          :value="Number(value)"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-date-picker
        v-model="localParams.createTime"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="!w-240px"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { QueryParams } from '../types'
import { TASK_TYPE_LABELS, TASK_STATUS_LABELS } from '../constants'

const props = defineProps<{
  queryParams: QueryParams
}>()

const emit = defineEmits<{
  query: []
  reset: []
}>()

const queryFormRef = ref()
const localParams = reactive({ ...props.queryParams })

watch(
  () => props.queryParams,
  (newVal) => {
    Object.assign(localParams, newVal)
  },
  { deep: true }
)

const handleQuery = () => {
  Object.assign(props.queryParams, localParams)
  emit('query')
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  emit('reset')
}
</script>

<style scoped>
</style>
